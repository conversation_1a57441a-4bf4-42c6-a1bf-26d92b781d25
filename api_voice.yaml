openapi: 3.1.0
info:
  title: CosyVoice2 跨语种复刻 API
  description: 跨语种复刻 API - Cross-lingual Voice Cloning with CosyVoice2-0.5B
  version: 2.0.0
paths:
  /api/v1/voices/:
    post:
      tags:
        - Voice Management
      summary: Create Voice
      description: Create a new voice in the cache
      operationId: create_voice_api_v1_voices__post
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_create_voice_api_v1_voices__post'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VoiceResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
        - Voice Management
      summary: List Voices
      description: List all voices with optional filtering and pagination
      operationId: list_voices_api_v1_voices__get
      parameters:
        - name: voice_type
          in: query
          required: false
          schema:
            anyOf:
              - $ref: '#/components/schemas/VoiceType'
              - type: 'null'
            description: Filter by voice type
            title: Voice Type
          description: Filter by voice type
        - name: language
          in: query
          required: false
          schema:
            anyOf:
              - type: string
              - type: 'null'
            description: Filter by language
            title: Language
          description: Filter by language
        - name: page
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            description: Page number
            default: 1
            title: Page
          description: Page number
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            maximum: 100
            minimum: 1
            description: Page size
            default: 50
            title: Page Size
          description: Page size
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VoiceListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/voices/{voice_id}:
    get:
      tags:
        - Voice Management
      summary: Get Voice
      description: Get a specific voice by ID
      operationId: get_voice_api_v1_voices__voice_id__get
      parameters:
        - name: voice_id
          in: path
          required: true
          schema:
            type: string
            title: Voice Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VoiceResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
        - Voice Management
      summary: Update Voice
      description: Update a voice's information
      operationId: update_voice_api_v1_voices__voice_id__put
      parameters:
        - name: voice_id
          in: path
          required: true
          schema:
            type: string
            title: Voice Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VoiceUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VoiceResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
        - Voice Management
      summary: Delete Voice
      description: Delete a voice from the cache
      operationId: delete_voice_api_v1_voices__voice_id__delete
      parameters:
        - name: voice_id
          in: path
          required: true
          schema:
            type: string
            title: Voice Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/voices/stats/summary:
    get:
      tags:
        - Voice Management
      summary: Get Voice Stats
      description: Get voice cache statistics
      operationId: get_voice_stats_api_v1_voices_stats_summary_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VoiceStats'
  /api/v1/voices/pretrained/list:
    get:
      tags:
        - Voice Management
      summary: List Pretrained Voices
      description: Get list of available pre-trained voices
      operationId: list_pretrained_voices_api_v1_voices_pretrained_list_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/cross-lingual/with-audio:
    post:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Cross Lingual With Audio
      description: |-
        跨语种复刻 - 带音频文件 (Cross-lingual voice cloning with audio file)

        Exactly like repo gốc CosyVoice: chỉ cần text và prompt_audio.
        KHÔNG sử dụng prompt_text hay instruct_text như repo gốc.
      operationId: cross_lingual_with_audio_api_v1_cross_lingual_with_audio_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_cross_lingual_with_audio_api_v1_cross_lingual_with_audio_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SynthesisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/with-cache:
    post:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Cross Lingual With Cache
      description: 跨语种复刻 - 使用缓存语音 (Cross-lingual voice cloning with cached voice)
      operationId: cross_lingual_with_cache_api_v1_cross_lingual_with_cache_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CrossLingualWithCacheRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SynthesisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/audio/{filename}:
    get:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Get Audio File
      description: Serve generated audio files
      operationId: get_audio_file_api_v1_cross_lingual_audio__filename__get
      parameters:
        - name: filename
          in: path
          required: true
          schema:
            type: string
            title: Filename
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/async:
    get:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: List Async Tasks
      description: 列出所有异步任务 (List all async tasks)
      operationId: list_async_tasks_api_v1_cross_lingual_async_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                title: Response List Async Tasks Api V1 Cross Lingual Async Get
    post:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Create Async Synthesis Task
      description: 创建异步跨语种复刻任务 (Create async cross-lingual synthesis task)
      operationId: create_async_synthesis_task_api_v1_cross_lingual_async_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CrossLingualAsyncRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncTaskResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/async/{task_id}:
    get:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Get Async Task Status
      description: 查询异步任务状态 (Get async task status)
      operationId: get_async_task_status_api_v1_cross_lingual_async__task_id__get
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncTaskStatusResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
        - 跨语种复刻 (Cross-lingual Voice Cloning)
      summary: Cancel Async Task
      description: 取消异步任务 (Cancel async task)
      operationId: cancel_async_task_api_v1_cross_lingual_async__task_id__delete
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/task:
    post:
      summary: Create Synthesis Task
      description: |-
        Create a new synthesis task with pre-allocated file path

        This endpoint immediately returns a task ID and pre-allocated file path,
        then processes the synthesis in the background.
      operationId: create_synthesis_task_api_v1_cross_lingual_task_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/task/{task_id}:
    get:
      summary: Get Task Status
      description: Get task status and progress
      operationId: get_task_status_api_v1_cross_lingual_task__task_id__get
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskStatusResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      summary: Delete Task
      description: Delete a task and its associated file
      operationId: delete_task_api_v1_cross_lingual_task__task_id__delete
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/cross-lingual/tasks:
    get:
      summary: List Tasks
      description: List all tasks with optional status filter
      operationId: list_tasks_api_v1_cross_lingual_tasks_get
      parameters:
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 50
            title: Limit
        - name: status
          in: query
          required: false
          schema:
            anyOf:
              - type: string
              - type: 'null'
            title: Status
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/streaming/cross-lingual:
    post:
      tags:
        - streaming
      summary: Stream Cross Lingual Synthesis
      description: |-
        Stream cross-lingual synthesis with real-time audio chunks

        This endpoint returns audio data as it's generated, providing real-time streaming
        capabilities for voice synthesis. The audio is sent in chunks using HTTP streaming.
      operationId: stream_cross_lingual_synthesis_api_v1_streaming_cross_lingual_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_stream_cross_lingual_synthesis_api_v1_streaming_cross_lingual_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/streaming/cross-lingual/chunked:
    post:
      tags:
        - streaming
      summary: Stream Cross Lingual Chunked
      description: |-
        Stream cross-lingual synthesis with chunked transfer encoding

        This endpoint uses HTTP chunked transfer encoding to stream audio data
        in real-time. Each chunk contains raw audio data without additional headers.
      operationId: stream_cross_lingual_chunked_api_v1_streaming_cross_lingual_chunked_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_stream_cross_lingual_chunked_api_v1_streaming_cross_lingual_chunked_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/streaming/cross-lingual/progressive:
    post:
      tags:
        - streaming
      summary: Stream Cross Lingual Progressive
      description: |-
        Stream cross-lingual synthesis with progressive audio playback support

        This endpoint provides proper WAV streaming with header first, then raw PCM data.
        Designed for progressive audio playback in browsers.
      operationId: stream_cross_lingual_progressive_api_v1_streaming_cross_lingual_progressive_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_stream_cross_lingual_progressive_api_v1_streaming_cross_lingual_progressive_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/streaming/cross-lingual/sse:
    get:
      tags:
        - streaming
      summary: Stream Cross Lingual Sse
      description: |-
        Stream cross-lingual synthesis using Server-Sent Events for true progressive playback

        This endpoint uses SSE to send audio chunks as separate events, enabling
        true progressive audio playback in browsers.
      operationId: stream_cross_lingual_sse_api_v1_streaming_cross_lingual_sse_get
      parameters:
        - name: text
          in: query
          required: true
          schema:
            type: string
            title: Text
        - name: voice_id
          in: query
          required: true
          schema:
            type: string
            title: Voice Id
        - name: format
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/AudioFormat'
            default: wav
            title: Format
        - name: speed
          in: query
          required: false
          schema:
            type: number
            default: 1
            title: Speed
        - name: quality
          in: query
          required: false
          schema:
            allOf:
              - $ref: '#/components/schemas/StreamingQuality'
            default: medium
            title: Quality
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/streaming/health:
    get:
      tags:
        - streaming
      summary: Streaming Health Check
      description: Health check for streaming functionality
      operationId: streaming_health_check_api_v1_streaming_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/streaming/stats:
    get:
      tags:
        - streaming
      summary: Streaming Stats
      description: Get streaming performance statistics
      operationId: streaming_stats_api_v1_streaming_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/ws/sessions:
    get:
      tags:
        - websocket
      summary: Get Active Sessions
      description: Get information about active WebSocket sessions
      operationId: get_active_sessions_api_v1_ws_sessions_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /:
    get:
      summary: Root
      operationId: root__get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /health:
    get:
      summary: Health Check
      description: Health check endpoint
      operationId: health_check_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
components:
  schemas:
    AsyncTaskResponse:
      properties:
        success:
          type: boolean
          title: Success
          description: 任务创建是否成功
        task_id:
          type: string
          title: Task Id
          description: 任务ID
        message:
          type: string
          title: Message
          description: 响应消息
        status:
          type: string
          title: Status
          description: '任务状态: pending, processing, completed, failed'
        estimated_time:
          anyOf:
            - type: number
            - type: 'null'
          title: Estimated Time
          description: 预估完成时间(秒)
      type: object
      required:
        - success
        - task_id
        - message
        - status
      title: AsyncTaskResponse
      description: 异步任务响应
    AsyncTaskStatusResponse:
      properties:
        success:
          type: boolean
          title: Success
          description: 查询是否成功
        task_id:
          type: string
          title: Task Id
          description: 任务ID
        status:
          type: string
          title: Status
          description: '任务状态: pending, processing, completed, failed'
        progress:
          type: number
          title: Progress
          description: 任务进度 (0.0-1.0)
        message:
          type: string
          title: Message
          description: 状态消息
        audio_url:
          anyOf:
            - type: string
            - type: 'null'
          title: Audio Url
          description: 音频文件URL (仅completed状态)
        file_path:
          anyOf:
            - type: string
            - type: 'null'
          title: File Path
          description: 音频文件路径 (仅completed状态)
        duration:
          anyOf:
            - type: number
            - type: 'null'
          title: Duration
          description: 音频时长(秒) (仅completed状态)
        synthesis_time:
          anyOf:
            - type: number
            - type: 'null'
          title: Synthesis Time
          description: 合成耗时(秒) (仅completed状态)
        error_message:
          anyOf:
            - type: string
            - type: 'null'
          title: Error Message
          description: 错误信息 (仅failed状态)
        created_at:
          anyOf:
            - type: string
            - type: 'null'
          title: Created At
          description: 任务创建时间
        completed_at:
          anyOf:
            - type: string
            - type: 'null'
          title: Completed At
          description: 任务完成时间
      type: object
      required:
        - success
        - task_id
        - status
        - progress
        - message
      title: AsyncTaskStatusResponse
      description: 异步任务状态查询响应
    AudioFormat:
      type: string
      enum:
        - wav
        - mp3
        - flac
        - m4a
      title: AudioFormat
    Body_create_voice_api_v1_voices__post:
      properties:
        voice_id:
          type: string
          title: Voice Id
          description: Unique voice identifier
        name:
          type: string
          title: Name
          description: Human-readable voice name
        description:
          anyOf:
            - type: string
            - type: 'null'
          title: Description
          description: Voice description
        voice_type:
          allOf:
            - $ref: '#/components/schemas/VoiceType'
          description: Type of voice
        language:
          anyOf:
            - type: string
            - type: 'null'
          title: Language
          description: Primary language of the voice
        prompt_text:
          anyOf:
            - type: string
            - type: 'null'
          title: Prompt Text
          description: Text that matches the audio sample
        audio_format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: Audio file format
          default: wav
        audio_file:
          type: string
          format: binary
          title: Audio File
          description: Audio file for voice cloning
      type: object
      required:
        - voice_id
        - name
        - voice_type
        - audio_file
      title: Body_create_voice_api_v1_voices__post
    Body_cross_lingual_with_audio_api_v1_cross_lingual_with_audio_post:
      properties:
        text:
          type: string
          title: Text
          description: 要合成的文本 (Text to synthesize)
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: 输出音频格式
          default: wav
        speed:
          type: number
          title: Speed
          description: 语速倍数
          default: 1
        stream:
          type: boolean
          title: Stream
          description: '是否流式推理 (默认: 否)'
          default: false
        prompt_audio:
          type: string
          format: binary
          title: Prompt Audio
          description: 参考音频文件 (Reference audio file)
      type: object
      required:
        - text
        - prompt_audio
      title: Body_cross_lingual_with_audio_api_v1_cross_lingual_with_audio_post
    Body_stream_cross_lingual_chunked_api_v1_streaming_cross_lingual_chunked_post:
      properties:
        text:
          type: string
          maxLength: 2000
          title: Text
          description: Text to synthesize
        voice_id:
          type: string
          title: Voice Id
          description: Voice ID from cache
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: Audio format
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: Speech speed multiplier
          default: 1
        quality:
          allOf:
            - $ref: '#/components/schemas/StreamingQuality'
          description: Streaming quality
          default: medium
      type: object
      required:
        - text
        - voice_id
      title: Body_stream_cross_lingual_chunked_api_v1_streaming_cross_lingual_chunked_post
    Body_stream_cross_lingual_progressive_api_v1_streaming_cross_lingual_progressive_post:
      properties:
        text:
          type: string
          maxLength: 2000
          title: Text
          description: Text to synthesize
        voice_id:
          type: string
          title: Voice Id
          description: Voice ID from cache
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: Audio format
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: Speech speed multiplier
          default: 1
        quality:
          allOf:
            - $ref: '#/components/schemas/StreamingQuality'
          description: Streaming quality
          default: medium
      type: object
      required:
        - text
        - voice_id
      title: Body_stream_cross_lingual_progressive_api_v1_streaming_cross_lingual_progressive_post
    Body_stream_cross_lingual_synthesis_api_v1_streaming_cross_lingual_post:
      properties:
        text:
          type: string
          maxLength: 2000
          title: Text
          description: Text to synthesize
        voice_id:
          type: string
          title: Voice Id
          description: Voice ID from cache
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: Audio format
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: Speech speed multiplier
          default: 1
        quality:
          allOf:
            - $ref: '#/components/schemas/StreamingQuality'
          description: Streaming quality
          default: medium
        chunk_size:
          anyOf:
            - type: integer
              maximum: 8192
              minimum: 256
            - type: 'null'
          title: Chunk Size
          description: Chunk size in bytes
          default: 1024
      type: object
      required:
        - text
        - voice_id
      title: Body_stream_cross_lingual_synthesis_api_v1_streaming_cross_lingual_post
    CrossLingualAsyncRequest:
      properties:
        text:
          type: string
          maxLength: 2000
          title: Text
          description: 要合成的文本 (Text to synthesize)
        voice_id:
          type: string
          title: Voice Id
          description: 缓存中的语音ID (Voice ID from cache)
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: 输出音频格式
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: 语速倍数
          default: 1
        callback_url:
          anyOf:
            - type: string
            - type: 'null'
          title: Callback Url
          description: 完成后回调URL (Optional callback URL when completed)
      type: object
      required:
        - text
        - voice_id
      title: CrossLingualAsyncRequest
      description: |-
        跨语种复刻 - 异步任务请求 (Cross-lingual async task request)

        Exactly like repo gốc CosyVoice: chỉ cần text và voice_id.
        KHÔNG sử dụng prompt_text hay instruct_text như repo gốc.
    CrossLingualWithCacheRequest:
      properties:
        text:
          type: string
          maxLength: 2000
          title: Text
          description: 要合成的文本 (Text to synthesize)
        voice_id:
          type: string
          title: Voice Id
          description: 缓存中的语音ID (Voice ID from cache)
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: 输出音频格式
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: 语速倍数
          default: 1
        stream:
          type: boolean
          title: Stream
          description: '是否流式推理 (默认: 否)'
          default: false
      type: object
      required:
        - text
        - voice_id
      title: CrossLingualWithCacheRequest
      description: |-
        跨语种复刻 - 使用缓存语音 (Cross-lingual voice cloning with cached voice)

        Exactly like repo gốc CosyVoice: chỉ cần text và voice_id.
        KHÔNG sử dụng prompt_text hay instruct_text như repo gốc.
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    StreamingQuality:
      type: string
      enum:
        - low
        - medium
        - high
      title: StreamingQuality
      description: Audio quality settings for streaming optimization
    SynthesisResponse:
      properties:
        success:
          type: boolean
          title: Success
          description: Whether synthesis was successful
        message:
          type: string
          title: Message
          description: Response message
        audio_url:
          anyOf:
            - type: string
            - type: 'null'
          title: Audio Url
          description: URL to download the generated audio
        file_path:
          anyOf:
            - type: string
            - type: 'null'
          title: File Path
          description: Local file path to generated audio
        duration:
          anyOf:
            - type: number
            - type: 'null'
          title: Duration
          description: Audio duration in seconds
        format:
          allOf:
            - $ref: '#/components/schemas/AudioFormat'
          description: Audio format
        synthesis_time:
          anyOf:
            - type: number
            - type: 'null'
          title: Synthesis Time
          description: Time taken for synthesis in seconds
      type: object
      required:
        - success
        - message
        - format
      title: SynthesisResponse
    TaskRequest:
      properties:
        text:
          type: string
          maxLength: 1000
          title: Text
          description: Text to synthesize
        voice_id:
          type: string
          title: Voice Id
          description: Cached voice ID
        format:
          type: string
          title: Format
          description: Output audio format
          default: wav
        speed:
          type: number
          maximum: 2
          minimum: 0.5
          title: Speed
          description: 语速倍数
          default: 1
      type: object
      required:
        - text
        - voice_id
      title: TaskRequest
      description: Task-based synthesis request
    TaskResponse:
      properties:
        task_id:
          type: string
          title: Task Id
          description: Unique task identifier
        status:
          type: string
          title: Status
          description: 'Task status: ''registered'', ''processing'', ''completed'', ''failed'''
        file_path:
          type: string
          title: File Path
          description: Pre-allocated file path
        audio_url:
          type: string
          title: Audio Url
          description: Pre-allocated audio URL
        estimated_duration:
          anyOf:
            - type: number
            - type: 'null'
          title: Estimated Duration
          description: Estimated processing time
        created_at:
          type: string
          title: Created At
          description: Task creation timestamp
      type: object
      required:
        - task_id
        - status
        - file_path
        - audio_url
        - created_at
      title: TaskResponse
      description: Task registration response
    TaskStatusResponse:
      properties:
        task_id:
          type: string
          title: Task Id
          description: Task identifier
        status:
          type: string
          title: Status
          description: Current task status
        file_path:
          type: string
          title: File Path
          description: File path
        audio_url:
          type: string
          title: Audio Url
          description: Audio URL
        progress:
          type: number
          title: Progress
          description: Progress percentage (0.0-1.0)
          default: 0
        duration:
          anyOf:
            - type: number
            - type: 'null'
          title: Duration
          description: Actual audio duration
        synthesis_time:
          anyOf:
            - type: number
            - type: 'null'
          title: Synthesis Time
          description: Time taken for synthesis
        created_at:
          type: string
          title: Created At
          description: Task creation timestamp
        completed_at:
          anyOf:
            - type: string
            - type: 'null'
          title: Completed At
          description: Task completion timestamp
        error_message:
          anyOf:
            - type: string
            - type: 'null'
          title: Error Message
          description: Error message if failed
      type: object
      required:
        - task_id
        - status
        - file_path
        - audio_url
        - created_at
      title: TaskStatusResponse
      description: Task status response
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
              - type: string
              - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
        - loc
        - msg
        - type
      title: ValidationError
    VoiceListResponse:
      properties:
        voices:
          items:
            $ref: '#/components/schemas/VoiceResponse'
          type: array
          title: Voices
        total:
          type: integer
          title: Total
        page:
          type: integer
          title: Page
          default: 1
        per_page:
          type: integer
          title: Per Page
          default: 10
      type: object
      required:
        - voices
        - total
      title: VoiceListResponse
    VoiceResponse:
      properties:
        voice_id:
          type: string
          title: Voice Id
          description: Unique voice identifier
        name:
          type: string
          title: Name
          description: Human-readable voice name
        description:
          anyOf:
            - type: string
            - type: 'null'
          title: Description
          description: Voice description
        voice_type:
          allOf:
            - $ref: '#/components/schemas/VoiceType'
          description: Type of voice
        language:
          anyOf:
            - type: string
            - type: 'null'
          title: Language
          description: Primary language of the voice (supports multilingual)
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
        audio_format:
          $ref: '#/components/schemas/AudioFormat'
        file_size:
          anyOf:
            - type: integer
            - type: 'null'
          title: File Size
        duration:
          anyOf:
            - type: number
            - type: 'null'
          title: Duration
        sample_rate:
          anyOf:
            - type: integer
            - type: 'null'
          title: Sample Rate
        is_active:
          type: boolean
          title: Is Active
          default: true
      type: object
      required:
        - voice_id
        - name
        - voice_type
        - created_at
        - updated_at
        - audio_format
      title: VoiceResponse
    VoiceStats:
      properties:
        total_voices:
          type: integer
          title: Total Voices
          default: 0
        active_voices:
          type: integer
          title: Active Voices
          default: 0
        voice_types:
          type: object
          title: Voice Types
        languages:
          type: object
          title: Languages
        total_duration:
          type: number
          title: Total Duration
          default: 0
        total_size:
          type: integer
          title: Total Size
          default: 0
      type: object
      title: VoiceStats
    VoiceType:
      type: string
      enum:
        - sft
        - zero_shot
        - cross_lingual
        - instruct
      title: VoiceType
    VoiceUpdate:
      properties:
        name:
          anyOf:
            - type: string
            - type: 'null'
          title: Name
          description: Human-readable voice name
        description:
          anyOf:
            - type: string
            - type: 'null'
          title: Description
          description: Voice description
        language:
          anyOf:
            - type: string
            - type: 'null'
          title: Language
          description: Primary language of the voice
      type: object
      title: VoiceUpdate
