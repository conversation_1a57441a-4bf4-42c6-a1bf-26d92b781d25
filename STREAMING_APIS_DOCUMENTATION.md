# Voice Streaming APIs Documentation

## 🎵 Overview
Comprehensive real-time voice streaming APIs added to Foxy GPU API v2.0.0 based on `api_voice.yaml` specifications.

## 🚀 New Streaming Features

### ✅ **Real-time Audio Streaming**
- **HTTP Streaming**: Continuous audio data streaming
- **Chunked Transfer**: Optimized chunked transfer encoding
- **Multiple Quality Levels**: Low, medium, high quality options
- **Format Support**: WAV, MP3, FLAC audio formats

### ✅ **Non-blocking Architecture**
- **Async Streaming**: Uses `httpx.AsyncClient` with streaming
- **No App Blocking**: Streaming doesn't freeze other endpoints
- **Concurrent Streams**: Multiple streams can run simultaneously
- **Proper Error Handling**: Graceful error responses

## 📊 Streaming API Endpoints

### 🎵 Voice Streaming Group
**Tag**: `Voice Streaming`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/streaming/cross-lingual` | Real-time voice synthesis streaming |
| POST | `/api/v1/streaming/cross-lingual/chunked` | Chunked transfer encoding streaming |
| GET | `/api/v1/streaming/health` | Streaming service health check |
| GET | `/api/v1/streaming/stats` | Streaming performance statistics |

## 🔧 API Details

### 1. Stream Cross-lingual Synthesis
**POST** `/api/v1/streaming/cross-lingual`

**Form Parameters:**
```json
{
  "text": "Text to synthesize (max 2000 chars)",
  "voice_id": "Voice ID from cache",
  "format": "wav|mp3|flac (default: wav)",
  "speed": "0.5-2.0 (default: 1.0)",
  "quality": "low|medium|high (default: medium)",
  "chunk_size": "256-8192 bytes (default: 1024)"
}
```

**Response:**
- **Content-Type**: `audio/wav`, `audio/mpeg`, or `audio/flac`
- **Headers**: 
  - `Cache-Control: no-cache`
  - `Connection: keep-alive`
  - `X-Accel-Buffering: no`

### 2. Stream Cross-lingual Chunked
**POST** `/api/v1/streaming/cross-lingual/chunked`

**Form Parameters:**
```json
{
  "text": "Text to synthesize (max 2000 chars)",
  "voice_id": "Voice ID from cache", 
  "format": "wav|mp3|flac (default: wav)",
  "speed": "0.5-2.0 (default: 1.0)",
  "quality": "low|medium|high (default: medium)"
}
```

**Response:**
- **Transfer-Encoding**: `chunked`
- **Content-Type**: Audio format specific
- **Real-time Chunks**: Raw audio data without headers

### 3. Streaming Health Check
**GET** `/api/v1/streaming/health`

**Response Examples:**
```json
// Healthy
{
  "status": "healthy",
  "proxy_status": "healthy",
  "proxy_timestamp": "2025-09-23T15:36:50.123456",
  "voice_service_status": 200
}

// Unhealthy
{
  "status": "unreachable",
  "voice_service_endpoint": "http://localhost:8012",
  "proxy_status": "healthy",
  "proxy_timestamp": "2025-09-23T15:36:50.123456",
  "error": "Cannot connect to voice service streaming"
}
```

### 4. Streaming Statistics
**GET** `/api/v1/streaming/stats`

**Response:**
```json
{
  "active_streams": 5,
  "total_streams": 1250,
  "average_latency": 45.2,
  "bytes_streamed": **********,
  "quality_distribution": {
    "low": 100,
    "medium": 800,
    "high": 350
  },
  "uptime": 86400.0,
  "proxy_active": true,
  "service_available": true
}
```

## 🎯 Data Models

### StreamingQuality Enum
```python
class StreamingQuality(str, Enum):
    low = "low"        # Optimized for speed
    medium = "medium"  # Balanced quality/speed
    high = "high"      # Maximum quality
```

### StreamCrossLingualRequest
```python
class StreamCrossLingualRequest(BaseModel):
    text: str = Field(max_length=2000)
    voice_id: str
    format: AudioFormat = AudioFormat.wav
    speed: float = Field(1.0, ge=0.5, le=2.0)
    quality: StreamingQuality = StreamingQuality.medium
    chunk_size: Optional[int] = Field(1024, ge=256, le=8192)
```

### StreamingStats
```python
class StreamingStats(BaseModel):
    active_streams: int = 0
    total_streams: int = 0
    average_latency: float = 0.0
    bytes_streamed: int = 0
    quality_distribution: Dict[str, int] = {}
    uptime: float = 0.0
```

## 🔄 Streaming Implementation

### Async Stream Generator
```python
async def stream_audio_from_service(url: str, data: dict) -> AsyncGenerator[bytes, None]:
    """Stream audio data from voice service"""
    async with httpx.AsyncClient(timeout=60.0) as client:
        async with client.stream("POST", url, data=data) as response:
            if response.status_code == 200:
                async for chunk in response.aiter_bytes(chunk_size=1024):
                    if chunk:
                        yield chunk
```

### StreamingResponse Usage
```python
return StreamingResponse(
    stream_audio_from_service(stream_url, form_data),
    media_type="audio/wav",
    headers={
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "X-Accel-Buffering": "no"
    }
)
```

## 📝 Usage Examples

### cURL Examples

#### Basic Streaming
```bash
curl -X POST "http://localhost:8000/api/v1/streaming/cross-lingual" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "text=Hello world&voice_id=my_voice&format=wav&quality=medium" \
  --output streamed_audio.wav
```

#### Chunked Streaming
```bash
curl -X POST "http://localhost:8000/api/v1/streaming/cross-lingual/chunked" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "text=Hello world&voice_id=my_voice&format=mp3&speed=1.2&quality=high" \
  --output chunked_audio.mp3
```

#### Health Check
```bash
curl "http://localhost:8000/api/v1/streaming/health"
```

#### Statistics
```bash
curl "http://localhost:8000/api/v1/streaming/stats"
```

### JavaScript/Fetch Example
```javascript
// Stream audio with fetch
const response = await fetch('/api/v1/streaming/cross-lingual', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    text: 'Hello world',
    voice_id: 'my_voice',
    format: 'wav',
    quality: 'medium'
  })
});

// Read stream
const reader = response.body.getReader();
const chunks = [];

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  chunks.push(value);
}

// Combine chunks into audio blob
const audioBlob = new Blob(chunks, { type: 'audio/wav' });
```

## ⚡ Performance Features

### 🚀 **Optimizations**
- **Async Streaming**: Non-blocking audio generation
- **Chunked Transfer**: Efficient data transmission
- **Quality Control**: Adjustable quality vs speed tradeoffs
- **Configurable Chunks**: Customizable chunk sizes (256-8192 bytes)

### 📊 **Monitoring**
- **Real-time Stats**: Active streams, latency, throughput
- **Health Checks**: Service availability monitoring
- **Quality Metrics**: Usage distribution by quality level
- **Error Tracking**: Comprehensive error handling and logging

## 🔧 Configuration

### Environment Variables
```bash
ENDPOINT_VOICE=http://localhost:8012  # Voice service endpoint
```

### Quality Settings Impact
- **Low**: Fastest generation, lower audio quality
- **Medium**: Balanced performance and quality (default)
- **High**: Best audio quality, slower generation

## 🎯 Benefits

1. **🎵 Real-time Audio**: Immediate audio playback as it's generated
2. **⚡ Low Latency**: Streaming reduces perceived wait time
3. **📊 Scalable**: Handle multiple concurrent streams
4. **🔧 Flexible**: Multiple quality and format options
5. **📈 Monitorable**: Comprehensive health and performance metrics
6. **🚫 Non-blocking**: Doesn't interfere with other API operations

## 🔄 Integration with Existing APIs

The streaming APIs complement existing voice generation endpoints:

- **Batch Processing**: Use `/api/v1/cross-lingual/with-cache` for file-based generation
- **Real-time Streaming**: Use `/api/v1/streaming/cross-lingual` for immediate playback
- **Task Management**: Use `/api/v1/cross-lingual/task` for heavy workloads
- **File Serving**: Generated files still accessible via file serving endpoints

## 📚 Documentation Access

- **Interactive Docs**: `http://localhost:8000/docs` (Voice Streaming section)
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

The streaming APIs are now fully integrated and ready for real-time voice synthesis! 🎉
