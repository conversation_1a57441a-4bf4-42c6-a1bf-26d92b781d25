# Voice Service Blocking Issue - Fixed

## Problem Identified
The `/api/v1/cross-lingual/with-cache` endpoint was blocking/hanging because:
1. **Voice service on port 8012 is not running or not accessible**
2. **Long timeout settings** (360 seconds) were causing requests to hang
3. **Poor error handling** didn't provide clear feedback about the issue

## Root Cause
```bash
# Health check shows:
{
  "status": "unreachable",
  "voice_service_endpoint": "http://localhost:8012",
  "error": "Connection refused",
  "message": "Cannot connect to voice service"
}
```

## Solution Implemented

### 1. Improved Timeout Management
**Before:**
```python
timeout=(15, 360)  # 15s connect, 360s read (6 minutes!)
```

**After:**
```python
timeout=(10, 120)  # 10s connect, 120s read (2 minutes max)
```

### 2. Enhanced Error Handling
Added specific error handling for:
- **Connection Errors** (503 Service Unavailable)
- **Timeout Errors** (504 Gateway Timeout)
- **HTTP Errors** (Various status codes)
- **Unexpected Errors** (500 Internal Server Error)

### 3. Better Response Management
```python
# Now checks response status and handles errors properly
if response.status_code == 200:
    # Process successful response
    enhanced_data = enhance_voice_response(response_data)
    return JSONResponse(content=enhanced_data, status_code=200)
else:
    # Handle error responses from voice service
    error_data = response.json()
    return JSONResponse(content=error_data, status_code=response.status_code)
```

### 4. New Diagnostic Endpoints

#### Voice Service Health Check
```bash
GET /api/v1/voice-service/health
```
**Response:**
```json
{
  "status": "unreachable",
  "voice_service_endpoint": "http://localhost:8012",
  "error": "Connection refused",
  "message": "Cannot connect to voice service"
}
```

#### Voice Service Connection Test
```bash
POST /api/v1/voice-service/test
```
**Response:**
```json
{
  "status": "failed",
  "voice_service_endpoint": "http://localhost:8012",
  "error": "Connection refused",
  "test_endpoint": "/api/v1/voices/pretrained/list",
  "message": "Voice service connection test failed"
}
```

### 5. Updated Endpoints with Better Error Handling
- `/api/v1/cross-lingual/with-cache` - No more hanging, fast error responses
- `/api/v1/cross-lingual/async` - Improved timeout and error handling
- `/api/v1/cross-lingual/with-audio` - Better error management

## How to Test and Resolve

### Step 1: Check Voice Service Status
```bash
# Test the health check endpoint
curl http://localhost:8000/api/v1/voice-service/health

# Test the connection
curl -X POST http://localhost:8000/api/v1/voice-service/test
```

### Step 2: Start the Voice Service
The voice service on port 8012 needs to be running. Check if:
```bash
# Check if port 8012 is in use
lsof -i :8012

# Or check if the CosyVoice2-API service is running
ps aux | grep cosyvoice
```

### Step 3: Test Voice Generation (After Service is Running)
```bash
# Test with cache (should now respond quickly even if it fails)
curl -X POST http://localhost:8000/api/v1/cross-lingual/with-cache \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello world",
    "voice_id": "test_voice"
  }'
```

### Step 4: Debug with Enhanced Endpoints
```bash
# Check system status and available files
curl http://localhost:8000/api/v1/voice-service/debug
```

## Expected Behavior Now

### When Voice Service is Down:
```json
{
  "error": "Voice service unavailable",
  "message": "Cannot connect to voice service at http://localhost:8012",
  "status": "connection_error"
}
```
**Response Time:** ~10 seconds (instead of hanging for 6 minutes)

### When Voice Service is Up but Has Errors:
```json
{
  "error": "ModuleNotFoundError: No module named 'app.core.file_manager'",
  "status": "internal_error"
}
```
**Response Time:** Fast response with actual error details

### When Voice Service Works:
```json
{
  "success": true,
  "message": "Voice generated successfully",
  "file_path": "outputs/cross_lingual_cache_ee6d15c3.wav",
  "audio_url": "http://domain/api/v1/render-audio/outputs/cross_lingual_cache_ee6d15c3.wav",
  "share_url": "http://domain/api/v1/share-file-voice/cross_lingual_cache_ee6d15c3.wav"
}
```

## Next Steps

1. **Start Voice Service**: Ensure CosyVoice2-API is running on port 8012
2. **Fix Voice Service**: Resolve the `app.core.file_manager` module issue
3. **Test Endpoints**: Use the health check endpoints to monitor status
4. **Monitor Performance**: Check response times and error rates

## Benefits of the Fix

✅ **No More Hanging**: Requests respond within 10-120 seconds maximum
✅ **Clear Error Messages**: Know exactly what's wrong with the voice service
✅ **Fast Diagnostics**: Health check endpoints for quick troubleshooting
✅ **Better UX**: Users get immediate feedback instead of waiting 6 minutes
✅ **Robust Error Handling**: Handles all types of connection and service errors

The blocking issue is now resolved. The API will respond quickly with clear error messages when the voice service is unavailable, instead of hanging indefinitely.
