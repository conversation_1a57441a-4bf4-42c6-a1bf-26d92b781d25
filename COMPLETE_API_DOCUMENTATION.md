# Complete API Documentation - <PERSON>y GPU API v2.0.0

## 🎯 Overview
Comprehensive API for Voice & Image Processing with organized endpoint groups and non-blocking async implementation.

## 📊 API Groups & Endpoints

### 🎵 Voice Management
**Tag**: `Voice Management`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/voices/` | Create new voice profile |
| GET | `/api/v1/voices/` | List voices with filtering & pagination |
| GET | `/api/v1/voices/{voice_id}` | Get specific voice by ID |
| PUT | `/api/v1/voices/{voice_id}` | Update voice information |
| DELETE | `/api/v1/voices/{voice_id}` | Delete voice from cache |
| GET | `/api/v1/voices/stats/summary` | Get voice cache statistics |
| GET | `/api/v1/voices/pretrained/list` | List available pretrained voices |

### 🗣️ Cross-lingual Voice Generation
**Tag**: `Cross-lingual Voice Generation`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/cross-lingual/with-cache` | Generate voice using cached voice |
| POST | `/api/v1/cross-lingual/with-audio` | Generate voice with audio file upload |
| POST | `/api/v1/cross-lingual/async` | Create async voice generation task |
| GET | `/api/v1/cross-lingual/async/{task_id}` | Get async task status |
| DELETE | `/api/v1/cross-lingual/async/{task_id}` | Cancel async task |
| GET | `/api/v1/cross-lingual/async` | List all async tasks |

### 📋 Task Management (NEW)
**Tag**: `Task Management`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/cross-lingual/task` | Create synthesis task with pre-allocated path |
| GET | `/api/v1/cross-lingual/task/{task_id}` | Get task status and progress |
| DELETE | `/api/v1/cross-lingual/task/{task_id}` | Delete task and associated file |
| GET | `/api/v1/cross-lingual/tasks` | List all tasks with optional status filter |

### 📁 File Serving
**Tag**: `File Serving`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/cross-lingual/audio/{filename}` | Serve generated audio files |
| GET | `/api/v1/share-file-voice/{filename}` | Enhanced file sharing with CORS |
| GET | `/api/v1/render-audio/{file_path:path}` | Render audio from relative path |

### 🔧 System Diagnostics
**Tag**: `System Diagnostics`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/voice-service/health` | Check voice service health |
| POST | `/api/v1/voice-service/test` | Test voice service connection |
| GET | `/api/v1/voice-service/debug` | Debug system status & file discovery |

### 🖼️ Image Processing
**Tag**: `Image Processing` (Existing)

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/sdapi/v1/txt2img` | Create text-to-image generation task |
| POST | `/sdapi/v1/result/{id_task}` | Get image generation result |

## 🚀 Key Features

### ✅ Non-blocking Architecture
- **Async HTTP Client**: Uses `httpx` instead of `requests`
- **Fast Response Times**: 0.04-0.06 seconds for error responses
- **No App Freezing**: Multiple requests handled simultaneously
- **Docs Always Accessible**: `/docs` endpoint never blocked

### ✅ Enhanced Error Handling
```json
{
  "error": "Voice service unavailable",
  "message": "Cannot connect to voice service at http://localhost:8012",
  "status": "connection_error"
}
```

### ✅ Automatic URL Enhancement
Voice generation responses automatically include accessible URLs:
```json
{
  "success": true,
  "file_path": "outputs/cross_lingual_cache_ee6d15c3.wav",
  "audio_url": "http://domain/api/v1/render-audio/outputs/cross_lingual_cache_ee6d15c3.wav",
  "share_url": "http://domain/api/v1/share-file-voice/cross_lingual_cache_ee6d15c3.wav"
}
```

### ✅ Robust File Serving
- **Multiple Access Methods**: 3 different endpoints for file access
- **Fallback Mechanisms**: Searches multiple directories
- **CORS Support**: Enhanced cross-origin file sharing
- **Path Resolution**: Handles both relative and absolute paths

## 🔧 Configuration

### Environment Variables
```bash
ENDPOINT_VOICE=http://localhost:8012  # Voice service endpoint
IP_PUBLIC=http://*************:41965  # Public URL for file sharing
```

### File Storage Locations
```
/root/CosyVoice2-API/outputs/  # Primary voice file storage
/root/CosyVoice2-API/          # Secondary storage
/tmp/                          # Temporary files
./outputs/                     # Local outputs
```

## 📝 Usage Examples

### Voice Generation with Cache
```bash
curl -X POST http://localhost:8000/api/v1/cross-lingual/with-cache \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello world",
    "voice_id": "my_voice",
    "format": "wav",
    "speed": 1.0
  }'
```

### Task-based Generation (NEW)
```bash
# Create task
curl -X POST http://localhost:8000/api/v1/cross-lingual/task \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello world",
    "voice_id": "my_voice",
    "format": "wav",
    "speed": 1.0
  }'

# Check task status
curl http://localhost:8000/api/v1/cross-lingual/task/{task_id}
```

### File Access
```bash
# Method 1: Relative path
curl http://localhost:8000/api/v1/render-audio/outputs/file.wav

# Method 2: Filename only
curl http://localhost:8000/api/v1/share-file-voice/file.wav

# Method 3: Original endpoint
curl http://localhost:8000/api/v1/cross-lingual/audio/file.wav
```

### System Diagnostics
```bash
# Health check
curl http://localhost:8000/api/v1/voice-service/health

# Debug info
curl http://localhost:8000/api/v1/voice-service/debug
```

## 🎯 Benefits

1. **🚀 Performance**: Non-blocking async architecture
2. **📋 Organization**: Clear API grouping with tags
3. **🔄 Reliability**: Robust error handling and fallbacks
4. **📁 Flexibility**: Multiple file access methods
5. **🔧 Monitoring**: Comprehensive diagnostics
6. **📊 Scalability**: Task-based processing for heavy workloads
7. **🌐 CORS Ready**: Enhanced cross-origin support

## 🔄 Migration from v1.0.0

### New Features Added:
- **Task Management APIs**: Pre-allocated file paths and progress tracking
- **Enhanced File Serving**: Multiple access methods with fallbacks
- **System Diagnostics**: Health checks and debugging tools
- **Organized Tags**: Clear API grouping in documentation
- **Non-blocking Architecture**: Async HTTP client implementation

### Breaking Changes:
- None! All existing APIs remain compatible

## 📚 Documentation Access
- **Interactive Docs**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

The API is now production-ready with comprehensive voice processing capabilities, organized endpoint structure, and robust error handling! 🎉
