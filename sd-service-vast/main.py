from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
import requests
from PIL import Image
from starlette.concurrency import run_in_threadpool
import os
from dotenv import load_dotenv

load_dotenv()
# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

app = FastAPI(
    title="Ollama Local FastAPI Proxy",
    description="API để tương tác với Ollama cục bộ, hỗ trợ cả generate và chat API.",
    version="1.0.0"
)

# C<PERSON><PERSON> hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
class VoiceGenerateRequest(BaseModel):
    text: str
    history: Optional[List[str]] = None

@app.get("/")
async def read_root():
    return {"message": "Welcome to Ollama FastAPI Proxy! Use /generate or /chat to interact with Ollama."}


@app.get("/health")
async def health_check():
    try:
        return {"status": "healthy", "ollama_connected": True}
    except Exception as e:
        return {"status": "unhealthy", "ollama_connected": False, "error": str(e)}
@app.post("/sdapi/v1/txt2img")
async def txt2img(request: Request):
    json_payload = await request.json()
    url_task = "http://localhost:7860/agent-scheduler/v1/queue/txt2img"
    url_queue = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/position"
    try:
        response_task = requests.post(url=url_task, json=json_payload, timeout=(15, 360))
        data_task = response_task.json()
        response_queue = requests.get(url=url_queue.format(id_task=data_task["task_id"]), timeout=(15, 360))
        data_queue = response_queue.json()
        result = {
            "task": data_task,
            "queue": data_queue,
            "queue_time": (data_queue["data"]["position"] + 1) * 10,
            "ip_process": os.environ.get("IP_PUBLIC", "localhost")
        }
        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
@app.post("/sdapi/v1/result/{id_task}")
async def txt2img(id_task: str):
    url_result = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/results"
    url_queue = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/position"
    try:
        response_result = requests.get(url=url_result.format(id_task=id_task), timeout=(15, 360))
        data_result = response_result.json()
        output_data = {
            "success": data_result["success"]
        }
        if data_result["success"]:
            output_data["data"] = data_result["data"]
        else:
            response_queue = requests.get(url=url_queue.format(id_task=id_task), timeout=(15, 360))
            data_queue = response_queue.json()
            data_queue["data"]["success"] = data_result["success"]
            output_data["queue"] = data_queue
            output_data["queue_time"] = (data_queue["data"]["position"] + 1) * 8 
        return JSONResponse(content=output_data, status_code=200)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
