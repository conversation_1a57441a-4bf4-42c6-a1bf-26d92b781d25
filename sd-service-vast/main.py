from fastapi import FastAP<PERSON>, HTTPException, Request, File, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse, FileResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import logging
import requests
from PIL import Image
import os
from dotenv import load_dotenv
from datetime import datetime

load_dotenv()
# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

app = FastAPI(
    title="Foxy GPU API",
    description="API để tương tác với ảnh và voice",
    version="1.0.0"
)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Voice API Models
class AudioFormat(str, Enum):
    wav = "wav"
    mp3 = "mp3"
    flac = "flac"

class VoiceType(str, Enum):
    sft = "sft"
    zero_shot = "zero_shot"
    cross_lingual = "cross_lingual"
    instruct = "instruct"

class VoiceResponse(BaseModel):
    voice_id: str = Field(description="Unique voice identifier")
    name: str = Field(description="Human-readable voice name")
    description: Optional[str] = Field(None, description="Voice description")
    voice_type: VoiceType = Field(description="Type of voice")
    language: Optional[str] = Field(None, description="Primary language of the voice")
    created_at: datetime
    updated_at: datetime
    audio_format: AudioFormat
    file_size: Optional[int] = None
    duration: Optional[float] = None
    sample_rate: Optional[int] = None
    is_active: bool = True

class VoiceListResponse(BaseModel):
    voices: List[VoiceResponse]
    total: int
    page: int = 1
    per_page: int = 10

class VoiceUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Human-readable voice name")
    description: Optional[str] = Field(None, description="Voice description")
    language: Optional[str] = Field(None, description="Primary language of the voice")

class CrossLingualWithCacheRequest(BaseModel):
    text: str = Field(max_length=2000, description="要合成的文本 (Text to synthesize)")
    voice_id: str = Field(description="缓存中的语音ID (Voice ID from cache)")
    prompt_text: Optional[str] = Field(None, description="输入prompt文本 (Optional prompt text for voice reference)")
    instruct_text: Optional[str] = Field(None, description="输入instruct文本 (Optional instruction for voice style/emotion)")
    format: AudioFormat = Field(AudioFormat.wav, description="输出音频格式")
    speed: float = Field(1.0, ge=0.5, le=2.0, description="语速倍数")
    stream: bool = Field(False, description="是否流式推理 (默认: 否)")

class CrossLingualAsyncRequest(BaseModel):
    text: str = Field(max_length=2000, description="要合成的文本 (Text to synthesize)")
    voice_id: str = Field(description="缓存中的语音ID (Voice ID from cache)")
    prompt_text: Optional[str] = Field(None, description="输入prompt文本 (Optional prompt text for voice reference)")
    instruct_text: Optional[str] = Field(None, description="输入instruct文本 (Optional instruction for voice style/emotion)")
    format: AudioFormat = Field(AudioFormat.wav, description="输出音频格式")
    speed: float = Field(1.0, ge=0.5, le=2.0, description="语速倍数")
    callback_url: Optional[str] = Field(None, description="完成后回调URL (Optional callback URL when completed)")

class SynthesisResponse(BaseModel):
    success: bool = Field(description="Whether synthesis was successful")
    message: str = Field(description="Response message")
    audio_url: Optional[str] = Field(None, description="URL to download the generated audio")
    file_path: Optional[str] = Field(None, description="Local file path to generated audio")
    duration: Optional[float] = Field(None, description="Audio duration in seconds")
    format: AudioFormat = Field(description="Audio format")
    synthesis_time: Optional[float] = Field(None, description="Time taken for synthesis in seconds")

class AsyncTaskResponse(BaseModel):
    success: bool = Field(description="任务创建是否成功")
    task_id: str = Field(description="任务ID")
    message: str = Field(description="响应消息")
    status: str = Field(description="任务状态: pending, processing, completed, failed")
    estimated_time: Optional[float] = Field(None, description="预估完成时间(秒)")

class AsyncTaskStatusResponse(BaseModel):
    success: bool = Field(description="查询是否成功")
    task_id: str = Field(description="任务ID")
    status: str = Field(description="任务状态: pending, processing, completed, failed")
    progress: float = Field(description="任务进度 (0.0-1.0)")
    message: str = Field(description="状态消息")
    audio_url: Optional[str] = Field(None, description="音频文件URL (仅completed状态)")
    file_path: Optional[str] = Field(None, description="音频文件路径 (仅completed状态)")
    duration: Optional[float] = Field(None, description="音频时长(秒) (仅completed状态)")
    synthesis_time: Optional[float] = Field(None, description="合成耗时(秒) (仅completed状态)")
    error_message: Optional[str] = Field(None, description="错误信息 (仅failed状态)")
    created_at: Optional[str] = Field(None, description="任务创建时间")
    completed_at: Optional[str] = Field(None, description="任务完成时间")

class VoiceStats(BaseModel):
    total_voices: int = 0
    active_voices: int = 0
    voice_types: Dict[str, Any] = {}
    languages: Dict[str, Any] = {}
    total_duration: float = 0
    total_size: int = 0

class VoiceGenerateRequest(BaseModel):
    text: str
    history: Optional[List[str]] = None

@app.get("/")
async def read_root():
    return {"message": "Welcome to Ollama FastAPI Proxy! Use /generate or /chat to interact with Ollama."}


@app.get("/health")
async def health_check():
    try:
        return {"status": "healthy", "ollama_connected": True}
    except Exception as e:
        return {"status": "unhealthy", "ollama_connected": False, "error": str(e)}
@app.post("/sdapi/v1/txt2img")
async def txt2img(request: Request):
    json_payload = await request.json()
    url_task = "http://localhost:7860/agent-scheduler/v1/queue/txt2img"
    url_queue = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/position"
    try:
        response_task = requests.post(url=url_task, json=json_payload, timeout=(15, 360))
        data_task = response_task.json()
        response_queue = requests.get(url=url_queue.format(id_task=data_task["task_id"]), timeout=(15, 360))
        data_queue = response_queue.json()
        result = {
            "task": data_task,
            "queue": data_queue,
            "queue_time": (data_queue["data"]["position"] + 1) * 10,
            "ip_process": os.environ.get("IP_PUBLIC", "localhost")
        }
        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
@app.post("/sdapi/v1/result/{id_task}")
async def txt2img(id_task: str):
    url_result = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/results"
    url_queue = "http://localhost:7860/agent-scheduler/v1/task/{id_task}/position"
    try:
        response_result = requests.get(url=url_result.format(id_task=id_task), timeout=(15, 360))
        data_result = response_result.json()
        output_data = {
            "success": data_result["success"]
        }
        if data_result["success"]:
            output_data["data"] = data_result["data"]
        else:
            response_queue = requests.get(url=url_queue.format(id_task=id_task), timeout=(15, 360))
            data_queue = response_queue.json()
            data_queue["data"]["success"] = data_result["success"]
            output_data["queue"] = data_queue
            output_data["queue_time"] = (data_queue["data"]["position"] + 1) * 8 
        return JSONResponse(content=output_data, status_code=200)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

# Voice API Endpoints
ENDPOINT_VOICE = os.getenv("ENDPOINT_VOICE", "http://localhost:8012")

def convert_file_path_to_url(file_path: str, base_url: str = None) -> str:
    """Convert file_path from voice generation response to accessible URL"""
    if not file_path:
        return None

    if base_url is None:
        base_url = os.getenv("IP_PUBLIC", "http://localhost:8000")

    # Return URL for the render-audio endpoint that can handle relative paths
    return f"{base_url}/api/v1/render-audio/{file_path}"

def enhance_voice_response(response_data: dict) -> dict:
    """Enhance voice generation response with accessible audio URLs"""
    if isinstance(response_data, dict):
        # Create a copy to avoid modifying the original
        enhanced_data = response_data.copy()

        # Add audio_url if file_path exists
        if "file_path" in enhanced_data and enhanced_data["file_path"]:
            enhanced_data["audio_url"] = convert_file_path_to_url(enhanced_data["file_path"])
            # Also add a direct share URL
            enhanced_data["share_url"] = f"{os.getenv('IP_PUBLIC', 'http://localhost:8000')}/api/v1/share-file-voice/{os.path.basename(enhanced_data['file_path'])}"

        return enhanced_data

    return response_data

@app.post("/api/v1/voices/", response_model=VoiceResponse, status_code=201)
async def create_voice(
    voice_id: str = Form(..., description="Unique voice identifier"),
    name: str = Form(..., description="Human-readable voice name"),
    description: Optional[str] = Form(None, description="Voice description"),
    voice_type: VoiceType = Form(..., description="Type of voice"),
    language: Optional[str] = Form(None, description="Primary language of the voice"),
    prompt_text: Optional[str] = Form(None, description="Text that matches the audio sample"),
    audio_format: AudioFormat = Form(AudioFormat.wav, description="Audio file format"),
    audio_file: UploadFile = File(..., description="Audio file for voice cloning")
):
    """Create a new voice in the cache"""
    try:
        # Prepare form data for the voice service
        files = {"audio_file": (audio_file.filename, await audio_file.read(), audio_file.content_type)}
        data = {
            "voice_id": voice_id,
            "name": name,
            "voice_type": voice_type.value,
            "audio_format": audio_format.value
        }
        if description:
            data["description"] = description
        if language:
            data["language"] = language
        if prompt_text:
            data["prompt_text"] = prompt_text

        response = requests.post(
            f"{ENDPOINT_VOICE}/api/v1/voices/",
            files=files,
            data=data,
            timeout=(15, 360)
        )

        if response.status_code == 201:
            return JSONResponse(content=response.json(), status_code=201)
        else:
            return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/voices/", response_model=VoiceListResponse)
async def list_voices(
    voice_type: Optional[VoiceType] = None,
    language: Optional[str] = None,
    page: int = 1,
    page_size: int = 50
):
    """List all voices with optional filtering and pagination"""
    try:
        params = {
            "page": page,
            "page_size": page_size
        }
        if voice_type:
            params["voice_type"] = voice_type.value
        if language:
            params["language"] = language

        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/voices/",
            params=params,
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/voices/{voice_id}", response_model=VoiceResponse)
async def get_voice(voice_id: str):
    """Get a specific voice by ID"""
    try:
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/voices/{voice_id}",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.put("/api/v1/voices/{voice_id}", response_model=VoiceResponse)
async def update_voice(voice_id: str, voice_update: VoiceUpdate):
    """Update a voice's information"""
    try:
        response = requests.put(
            f"{ENDPOINT_VOICE}/api/v1/voices/{voice_id}",
            json=voice_update.model_dump(exclude_unset=True),
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.delete("/api/v1/voices/{voice_id}")
async def delete_voice(voice_id: str):
    """Delete a voice from the cache"""
    try:
        response = requests.delete(
            f"{ENDPOINT_VOICE}/api/v1/voices/{voice_id}",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.post("/api/v1/cross-lingual/with-cache", response_model=SynthesisResponse)
async def cross_lingual_with_cache(request: CrossLingualWithCacheRequest):
    """Cross-lingual voice cloning with cached voice"""
    try:
        # Use shorter timeout to prevent hanging
        response = requests.post(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/with-cache",
            json=request.model_dump(),
            timeout=(10, 120)  # 10s connect, 120s read (reduced from 360s)
        )

        if response.status_code == 200:
            # Enhance response with accessible URLs
            response_data = response.json()
            enhanced_data = enhance_voice_response(response_data)
            return JSONResponse(content=enhanced_data, status_code=200)
        else:
            # Return the error response from voice service
            try:
                error_data = response.json()
            except:
                error_data = {"error": f"Voice service returned status {response.status_code}"}
            return JSONResponse(content=error_data, status_code=response.status_code)

    except requests.exceptions.Timeout:
        return JSONResponse(
            content={
                "error": "Voice service timeout",
                "message": "The voice generation request timed out. The service may be overloaded or experiencing issues.",
                "status": "timeout"
            },
            status_code=504
        )
    except requests.exceptions.ConnectionError:
        return JSONResponse(
            content={
                "error": "Voice service unavailable",
                "message": f"Cannot connect to voice service at {ENDPOINT_VOICE}",
                "status": "connection_error"
            },
            status_code=503
        )
    except Exception as e:
        logging.error(f"Error in cross_lingual_with_cache: {str(e)}")
        return JSONResponse(
            content={
                "error": str(e),
                "message": "An unexpected error occurred during voice generation",
                "status": "internal_error"
            },
            status_code=500
        )

@app.post("/api/v1/cross-lingual/async", response_model=AsyncTaskResponse)
async def create_async_synthesis_task(request: CrossLingualAsyncRequest):
    """Create async cross-lingual synthesis task"""
    try:
        response = requests.post(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/async",
            json=request.model_dump(),
            timeout=(10, 60)  # Shorter timeout for task creation
        )

        if response.status_code == 200:
            return JSONResponse(content=response.json(), status_code=200)
        else:
            try:
                error_data = response.json()
            except:
                error_data = {"error": f"Voice service returned status {response.status_code}"}
            return JSONResponse(content=error_data, status_code=response.status_code)

    except requests.exceptions.Timeout:
        return JSONResponse(
            content={
                "error": "Voice service timeout",
                "message": "Task creation request timed out",
                "status": "timeout"
            },
            status_code=504
        )
    except requests.exceptions.ConnectionError:
        return JSONResponse(
            content={
                "error": "Voice service unavailable",
                "message": f"Cannot connect to voice service at {ENDPOINT_VOICE}",
                "status": "connection_error"
            },
            status_code=503
        )
    except Exception as e:
        logging.error(f"Error in create_async_synthesis_task: {str(e)}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/cross-lingual/async/{task_id}", response_model=AsyncTaskStatusResponse)
async def get_async_task_status(task_id: str):
    """Get async task status"""
    try:
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/async/{task_id}",
            timeout=(15, 360)
        )

        # Enhance response with accessible URLs
        response_data = response.json()
        enhanced_data = enhance_voice_response(response_data)

        return JSONResponse(content=enhanced_data, status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.delete("/api/v1/cross-lingual/async/{task_id}")
async def cancel_async_task(task_id: str):
    """Cancel async task"""
    try:
        response = requests.delete(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/async/{task_id}",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/cross-lingual/async")
async def list_async_tasks():
    """List all async tasks"""
    try:
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/async",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/voices/stats/summary", response_model=VoiceStats)
async def get_voice_stats():
    """Get voice cache statistics"""
    try:
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/voices/stats/summary",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/voices/pretrained/list")
async def list_pretrained_voices():
    """Get list of available pre-trained voices"""
    try:
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/voices/pretrained/list",
            timeout=(15, 360)
        )

        return JSONResponse(content=response.json(), status_code=response.status_code)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/cross-lingual/audio/{filename}")
async def get_audio_file(filename: str):
    """Serve generated audio files"""
    try:
        # First try to get from voice service
        try:
            response = requests.get(
                f"{ENDPOINT_VOICE}/api/v1/cross-lingual/audio/{filename}",
                timeout=(15, 60),
                stream=True
            )

            if response.status_code == 200:
                # Stream the audio file content
                def generate():
                    for chunk in response.iter_content(chunk_size=8192):
                        yield chunk

                return StreamingResponse(
                    generate(),
                    media_type=response.headers.get('content-type', 'audio/wav'),
                    headers={
                        "Content-Disposition": f"attachment; filename={filename}",
                        "Content-Length": response.headers.get('content-length', '')
                    }
                )
        except Exception as proxy_error:
            logging.warning(f"Voice service proxy failed: {proxy_error}")

        # If proxy fails, try to serve from common file locations
        possible_paths = [
            f"/root/CosyVoice2-API/outputs/{filename}",
            f"/root/CosyVoice2-API/{filename}",
            f"/tmp/{filename}",
            f"/tmp/audio/{filename}",
            f"/tmp/cosyvoice/{filename}",
            f"./audio/{filename}",
            f"./output/{filename}",
            f"./outputs/{filename}",
            f"../audio/{filename}"
        ]

        for file_path in possible_paths:
            if os.path.exists(file_path):
                return FileResponse(
                    file_path,
                    media_type="audio/wav",
                    filename=filename,
                    headers={"Content-Disposition": f"attachment; filename={filename}"}
                )

        return JSONResponse(
            content={"error": f"Audio file not found: {filename}"},
            status_code=404
        )

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/share-file-voice/{filename}")
async def share_file_voice(filename: str):
    """Share voice files from port 8012 through current API with enhanced fallback"""
    try:
        # First try to get from voice service
        try:
            response = requests.get(
                f"{ENDPOINT_VOICE}/api/v1/cross-lingual/audio/{filename}",
                timeout=(15, 60),
                stream=True
            )

            if response.status_code == 200:
                # Stream the audio file content
                def generate():
                    for chunk in response.iter_content(chunk_size=8192):
                        yield chunk

                return StreamingResponse(
                    generate(),
                    media_type=response.headers.get('content-type', 'audio/wav'),
                    headers={
                        "Content-Disposition": f"inline; filename={filename}",
                        "Content-Length": response.headers.get('content-length', ''),
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET",
                        "Access-Control-Allow-Headers": "*"
                    }
                )
        except Exception as proxy_error:
            logging.warning(f"Voice service proxy failed for sharing: {proxy_error}")

        # If proxy fails, try to serve from common file locations
        possible_paths = [
            f"/root/CosyVoice2-API/outputs/{filename}",
            f"/root/CosyVoice2-API/{filename}",
            f"/tmp/{filename}",
            f"/tmp/audio/{filename}",
            f"/tmp/cosyvoice/{filename}",
            f"/tmp/outputs/{filename}",
            f"./audio/{filename}",
            f"./output/{filename}",
            f"./outputs/{filename}",
            f"../audio/{filename}",
            f"../outputs/{filename}"
        ]

        for file_path in possible_paths:
            if os.path.exists(file_path):
                return FileResponse(
                    file_path,
                    media_type="audio/wav",
                    filename=filename,
                    headers={
                        "Content-Disposition": f"inline; filename={filename}",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET",
                        "Access-Control-Allow-Headers": "*"
                    }
                )

        return JSONResponse(
            content={
                "error": f"Audio file not found: {filename}",
                "message": "File not available in voice service or local storage",
                "searched_paths": possible_paths
            },
            status_code=404
        )

    except Exception as e:
        return JSONResponse(content={"error": f"Failed to share file: {str(e)}"}, status_code=500)

@app.post("/api/v1/cross-lingual/with-audio", response_model=SynthesisResponse)
async def cross_lingual_with_audio(
    text: str = Form(..., description="要合成的文本 (Text to synthesize)"),
    prompt_text: str = Form(..., description="输入prompt文本 (Reference text that matches the prompt audio)"),
    instruct_text: Optional[str] = Form(None, description="输入instruct文本 (Optional instruction for voice style/emotion)"),
    format: AudioFormat = Form(AudioFormat.wav, description="输出音频格式"),
    speed: float = Form(1.0, description="语速倍数"),
    stream: bool = Form(False, description="是否流式推理 (默认: 否)"),
    prompt_audio: UploadFile = File(..., description="参考音频文件 (Reference audio file)")
):
    """Cross-lingual voice cloning with audio file"""
    try:
        # Prepare form data for the voice service
        files = {"prompt_audio": (prompt_audio.filename, await prompt_audio.read(), prompt_audio.content_type)}
        data = {
            "text": text,
            "prompt_text": prompt_text,
            "format": format.value,
            "speed": speed,
            "stream": stream
        }
        if instruct_text:
            data["instruct_text"] = instruct_text

        response = requests.post(
            f"{ENDPOINT_VOICE}/api/v1/cross-lingual/with-audio",
            files=files,
            data=data,
            timeout=(10, 120)  # Reduced timeout
        )

        if response.status_code == 200:
            # Enhance response with accessible URLs
            response_data = response.json()
            enhanced_data = enhance_voice_response(response_data)
            return JSONResponse(content=enhanced_data, status_code=200)
        else:
            try:
                error_data = response.json()
            except:
                error_data = {"error": f"Voice service returned status {response.status_code}"}
            return JSONResponse(content=error_data, status_code=response.status_code)

    except requests.exceptions.Timeout:
        return JSONResponse(
            content={
                "error": "Voice service timeout",
                "message": "Voice generation with audio file timed out",
                "status": "timeout"
            },
            status_code=504
        )
    except requests.exceptions.ConnectionError:
        return JSONResponse(
            content={
                "error": "Voice service unavailable",
                "message": f"Cannot connect to voice service at {ENDPOINT_VOICE}",
                "status": "connection_error"
            },
            status_code=503
        )
    except Exception as e:
        logging.error(f"Error in cross_lingual_with_audio: {str(e)}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/voice-service/debug")
async def debug_voice_service():
    """Debug endpoint to check voice service status and available files"""
    try:
        debug_info = {
            "voice_service_endpoint": ENDPOINT_VOICE,
            "voice_service_status": "unknown",
            "available_audio_files": [],
            "searched_directories": []
        }

        # Test voice service connectivity
        try:
            response = requests.get(f"{ENDPOINT_VOICE}/health", timeout=5)
            debug_info["voice_service_status"] = "online" if response.status_code == 200 else f"error_{response.status_code}"
        except Exception as e:
            debug_info["voice_service_status"] = f"offline: {str(e)}"

        # Search for audio files in common locations
        search_dirs = [
            "/root/CosyVoice2-API/outputs",
            "/root/CosyVoice2-API",
            "/tmp",
            "/tmp/audio",
            "/tmp/cosyvoice",
            "/tmp/outputs",
            "./audio",
            "./output",
            "./outputs",
            "../audio",
            "../outputs"
        ]

        for search_dir in search_dirs:
            debug_info["searched_directories"].append(search_dir)
            if os.path.exists(search_dir):
                try:
                    files = [f for f in os.listdir(search_dir) if f.endswith(('.wav', '.mp3', '.flac'))]
                    if files:
                        debug_info["available_audio_files"].extend([
                            {"directory": search_dir, "file": f} for f in files[:10]  # Limit to 10 files per dir
                        ])
                except Exception as e:
                    debug_info["searched_directories"].append(f"{search_dir}: error - {str(e)}")

        return JSONResponse(content=debug_info, status_code=200)

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/render-audio/{file_path:path}")
async def render_audio_from_path(file_path: str):
    """Render audio file from relative path (e.g., outputs/cross_lingual_cache_ee6d15c3.wav)"""
    try:
        # Extract filename from path
        filename = os.path.basename(file_path)

        # Try different base paths for the relative path
        base_paths = [
            "/root/CosyVoice2-API",
            "/root/CosyVoice2-API/outputs",
            ".",
            ".."
        ]

        # First try the exact relative path from different base directories
        for base_path in base_paths:
            full_path = os.path.join(base_path, file_path)
            if os.path.exists(full_path):
                return FileResponse(
                    full_path,
                    media_type="audio/wav",
                    filename=filename,
                    headers={
                        "Content-Disposition": f"inline; filename={filename}",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET",
                        "Access-Control-Allow-Headers": "*"
                    }
                )

        # If relative path doesn't work, try just the filename in known directories
        possible_paths = [
            f"/root/CosyVoice2-API/outputs/{filename}",
            f"/root/CosyVoice2-API/{filename}",
            f"/tmp/{filename}",
            f"/tmp/outputs/{filename}",
            f"./outputs/{filename}",
            f"../outputs/{filename}"
        ]

        for full_path in possible_paths:
            if os.path.exists(full_path):
                return FileResponse(
                    full_path,
                    media_type="audio/wav",
                    filename=filename,
                    headers={
                        "Content-Disposition": f"inline; filename={filename}",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET",
                        "Access-Control-Allow-Headers": "*"
                    }
                )

        return JSONResponse(
            content={
                "error": f"Audio file not found: {file_path}",
                "filename": filename,
                "searched_paths": [os.path.join(bp, file_path) for bp in base_paths] + possible_paths
            },
            status_code=404
        )

    except Exception as e:
        return JSONResponse(content={"error": f"Failed to render audio: {str(e)}"}, status_code=500)

@app.get("/api/v1/voice-service/health")
async def check_voice_service_health():
    """Check if the voice service is healthy and responding"""
    try:
        # Test basic connectivity
        response = requests.get(f"{ENDPOINT_VOICE}/health", timeout=5)

        if response.status_code == 200:
            return JSONResponse(content={
                "status": "healthy",
                "voice_service_endpoint": ENDPOINT_VOICE,
                "response_time_ms": response.elapsed.total_seconds() * 1000,
                "message": "Voice service is responding normally"
            }, status_code=200)
        else:
            return JSONResponse(content={
                "status": "unhealthy",
                "voice_service_endpoint": ENDPOINT_VOICE,
                "error": f"Voice service returned status {response.status_code}",
                "message": "Voice service is not responding correctly"
            }, status_code=503)

    except requests.exceptions.Timeout:
        return JSONResponse(content={
            "status": "timeout",
            "voice_service_endpoint": ENDPOINT_VOICE,
            "error": "Connection timeout",
            "message": "Voice service is not responding within timeout period"
        }, status_code=504)

    except requests.exceptions.ConnectionError:
        return JSONResponse(content={
            "status": "unreachable",
            "voice_service_endpoint": ENDPOINT_VOICE,
            "error": "Connection refused",
            "message": "Cannot connect to voice service"
        }, status_code=503)

    except Exception as e:
        return JSONResponse(content={
            "status": "error",
            "voice_service_endpoint": ENDPOINT_VOICE,
            "error": str(e),
            "message": "Unexpected error checking voice service"
        }, status_code=500)

@app.post("/api/v1/voice-service/test")
async def test_voice_service_connection():
    """Test voice service connection with a simple request"""
    try:
        # Try to get the list of pretrained voices as a simple test
        response = requests.get(
            f"{ENDPOINT_VOICE}/api/v1/voices/pretrained/list",
            timeout=10
        )

        return JSONResponse(content={
            "status": "success" if response.status_code == 200 else "error",
            "voice_service_endpoint": ENDPOINT_VOICE,
            "status_code": response.status_code,
            "response_time_ms": response.elapsed.total_seconds() * 1000,
            "test_endpoint": "/api/v1/voices/pretrained/list",
            "message": "Voice service connection test completed"
        }, status_code=200)

    except Exception as e:
        return JSONResponse(content={
            "status": "failed",
            "voice_service_endpoint": ENDPOINT_VOICE,
            "error": str(e),
            "test_endpoint": "/api/v1/voices/pretrained/list",
            "message": "Voice service connection test failed"
        }, status_code=500)
