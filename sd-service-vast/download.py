import requests
import argparse

def download_model(url, file_name=None):
    # Token to add to the URL
    token = "5e559c2dea4d1ae7dff8150d3c2bc306"

    # Add token to the URL if not already present
    if "token=" not in url:
        separator = "&" if "?" in url else "?"
        file_url = f"{url}{separator}token={token}"
    else:
        file_url = url

    # If no file name is provided, try to extract it from URL or use default
    if not file_name:
        try:
            # Try to extract model ID from URL
            model_part = file_url.split("models/")[1].split("?")[0]
            file_name = f"model_{model_part}.safetensors"
        except:
            file_name = "downloaded_model.safetensors"
    else:
        # Ensure the file has the correct extension
        if not file_name.endswith('.safetensors'):
            file_name += '.safetensors'

    try:
        print(f"Attempting to download from: {file_url}")
        response = requests.get(file_url, stream=True)
        response.raise_for_status()  # Raise an exception for bad status codes

        # You could try to get the filename from headers if available
        # if 'Content-Disposition' in response.headers:
        #     cd = response.headers['Content-Disposition']
        #     file_name = cd.split('filename=')[1].strip('"')

        # Save to current directory
        download_path = file_name

        print(f"Downloading to: {download_path}")
        with open(download_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        print(f"Download complete: {download_path}")
        return True

    except requests.exceptions.RequestException as e:
        print(f"Error during download: {e}")
        return False
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return False

if __name__ == "__main__":
    # Set up command line argument parsing
    parser = argparse.ArgumentParser(description='Download a model from Civitai')
    parser.add_argument('url', type=str, help='The URL to download (e.g., https://civitai.com/api/download/models/992946)')
    parser.add_argument('--name', '-n', type=str, help='Name for the downloaded file (optional)')

    args = parser.parse_args()

    # Call the download function with the provided arguments
    download_model(args.url, args.name)